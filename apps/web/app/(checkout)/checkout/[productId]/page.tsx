// app/[locale]/(checkout)/checkout/[productId]/page.tsx
import "server-only";
import { redirect } from 'next/navigation';
import { CheckoutForm } from '../components/checkout-form';
import { CheckoutHeader } from '../components/checkout-header';
import { CheckoutBanner } from '../components/CheckoutBanner';
import { UrgencyBar } from '../components/urgency-bar';
import { TrustBadges } from '../components/trust-badges';
import { GuaranteeCards } from '../components/guarantee-cards';
import { ScarcityIndicator } from '../components/scarcity-indicator';
import { Testimonials } from '../components/testimonials';
import { CheckoutSidebar } from '../components/checkout-sidebar';
import { db } from '@repo/database';

interface CheckoutPageProps {
	params: Promise<{
		productId: string;
	}>;
	searchParams: Promise<{
		offer?: string;
		org?: string;
		quick?: string;
	}>;
}

export default async function CheckoutPage({ params, searchParams }: CheckoutPageProps) {
	const { productId } = await params;
	const { offer: offerId, org: organizationSlug, quick } = await searchParams;

	if (!productId) {
		redirect('/');
	}

	try {
		const product = await db.product.findFirst({
			where: {
				id: productId,
				status: {
					in: ['PUBLISHED', 'DRAFT'] // Aceitar tanto PUBLISHED quanto DRAFT
				},
			},
		});

		if (!product) {
			redirect('/');
		}

		// Buscar creator e offers separadamente
		const [creator, offers] = await Promise.all([
			db.user.findUnique({
				where: { id: product.creatorId },
				select: { id: true, name: true },
			}),
			db.offer.findMany({
				where: {
					productId: product.id,
					isActive: true,
					type: 'ORDER_BUMP',
				},
				select: {
					id: true,
					name: true,
					valueCents: true,
					type: true,
				},
			}),
		]);

		console.log('product', {
			id: product.id,
			settings: product.settings,
		});

		// Handle external checkout redirection
		if (product.checkoutType === 'EXTERNAL' && product.settings) {
			const settings = product.settings as Record<string, unknown>;
			if (typeof settings.customCheckoutUrl === 'string') {
				redirect(settings.customCheckoutUrl);
			}
		}

		// Parse checkout settings
		const checkoutSettings = product.settings as Record<string, unknown> || {};

		// Banner settings
		const bannerSettings = {
			enabled: (checkoutSettings.banner as Record<string, unknown>)?.enabled as boolean || false,
			url: (checkoutSettings.banner as Record<string, unknown>)?.url as string || null,
			maxHeight: (checkoutSettings.banner as Record<string, unknown>)?.maxHeight as string || '300px',
			borderRadius: (checkoutSettings.banner as Record<string, unknown>)?.borderRadius as string || 'rounded-lg',
			shadow: (checkoutSettings.banner as Record<string, unknown>)?.shadow as boolean || true,
		};

		// Header settings
		const headerSettings = {
			showLogo: (checkoutSettings.header as Record<string, unknown>)?.showLogo as boolean || false,
			logoUrl: (checkoutSettings.header as Record<string, unknown>)?.logoUrl as string || null,
			companyName: (checkoutSettings.header as Record<string, unknown>)?.companyName as string || "SupGateway",
		};

		// Urgency settings
		const urgencySettings = {
			enabled: (checkoutSettings.urgency as Record<string, unknown>)?.enabled as boolean || false,
			message: (checkoutSettings.urgency as Record<string, unknown>)?.message as string || "Esta oferta se encerra em:",
			endTime: (checkoutSettings.urgency as Record<string, unknown>)?.endTime ? new Date((checkoutSettings.urgency as Record<string, unknown>).endTime as string) : undefined,
			backgroundColor: (checkoutSettings.urgency as Record<string, unknown>)?.backgroundColor as string || "bg-red-50",
			textColor: (checkoutSettings.urgency as Record<string, unknown>)?.textColor as string || "text-white",
			accentColor: (checkoutSettings.urgency as Record<string, unknown>)?.accentColor as string || "bg-red-600",
		};

		// Trust badges settings
		const trustBadgesSettings = {
			enabled: (checkoutSettings.trustBadges as Record<string, unknown>)?.enabled as boolean || true,
			badges: (checkoutSettings.trustBadges as Record<string, unknown>)?.badges as any[] || [
				{
					id: 'security',
					title: '100% Seguro',
					subtitle: 'Pagamentos protegidos',
					icon: 'shield',
					enabled: true,
				},
				{
					id: 'guarantee',
					title: 'Garantia de 30 dias',
					subtitle: 'Devolução garantida',
					icon: 'check',
					enabled: true,
				},
			],
			layout: (checkoutSettings.trustBadges as Record<string, unknown>)?.layout as 'horizontal' | 'vertical' | 'grid' || 'vertical',
			showDescriptions: (checkoutSettings.trustBadges as Record<string, unknown>)?.showDescriptions as boolean || true,
			backgroundColor: (checkoutSettings.trustBadges as Record<string, unknown>)?.backgroundColor as string || 'bg-blue-50',
			textColor: (checkoutSettings.trustBadges as Record<string, unknown>)?.textColor as string || 'text-blue-800',
			borderColor: (checkoutSettings.trustBadges as Record<string, unknown>)?.borderColor as string || 'border-blue-200',
		};

		// Guarantee cards settings
		const guaranteeCardsSettings = {
			enabled: (checkoutSettings.guaranteeCards as Record<string, unknown>)?.enabled as boolean || false,
			cards: (checkoutSettings.guaranteeCards as Record<string, unknown>)?.cards as any[] || [],
			layout: (checkoutSettings.guaranteeCards as Record<string, unknown>)?.layout as 'horizontal' | 'vertical' | 'grid' || 'horizontal',
			backgroundColor: (checkoutSettings.guaranteeCards as Record<string, unknown>)?.backgroundColor as string || 'bg-green-50',
			textColor: (checkoutSettings.guaranteeCards as Record<string, unknown>)?.textColor as string || 'text-green-800',
			borderColor: (checkoutSettings.guaranteeCards as Record<string, unknown>)?.borderColor as string || 'border-green-200',
		};

		// Scarcity settings
		const scarcitySettings = {
			enabled: (checkoutSettings.scarcity as Record<string, unknown>)?.enabled as boolean || false,
			totalStock: (checkoutSettings.scarcity as Record<string, unknown>)?.totalStock as number || 100,
			soldCount: (checkoutSettings.scarcity as Record<string, unknown>)?.soldCount as number || 0,
			message: (checkoutSettings.scarcity as Record<string, unknown>)?.message as string || "Apenas {remaining} vagas restantes!",
			variant: (checkoutSettings.scarcity as Record<string, unknown>)?.variant as 'warning' | 'danger' | 'info' || 'warning',
			showIcon: (checkoutSettings.scarcity as Record<string, unknown>)?.showIcon as boolean || true,
			backgroundColor: (checkoutSettings.scarcity as Record<string, unknown>)?.backgroundColor as string || 'bg-orange-50',
			textColor: (checkoutSettings.scarcity as Record<string, unknown>)?.textColor as string || 'text-orange-800',
			borderColor: (checkoutSettings.scarcity as Record<string, unknown>)?.borderColor as string || 'border-orange-200',
		};

		// Testimonials settings
		const testimonialsSettings = {
			enabled: (checkoutSettings.testimonials as Record<string, unknown>)?.enabled as boolean || false,
			testimonials: (checkoutSettings.testimonials as Record<string, unknown>)?.testimonials as any[] || [],
			maxTestimonials: (checkoutSettings.testimonials as Record<string, unknown>)?.maxTestimonials as number || 3,
			autoPlay: (checkoutSettings.testimonials as Record<string, unknown>)?.autoPlay as boolean || true,
			autoPlayInterval: (checkoutSettings.testimonials as Record<string, unknown>)?.autoPlayInterval as number || 5,
			showControls: (checkoutSettings.testimonials as Record<string, unknown>)?.showControls as boolean || true,
			showStars: (checkoutSettings.testimonials as Record<string, unknown>)?.showStars as boolean || true,
			showAvatars: (checkoutSettings.testimonials as Record<string, unknown>)?.showAvatars as boolean || true,
			backgroundColor: (checkoutSettings.testimonials as Record<string, unknown>)?.backgroundColor as string || 'bg-gray-50',
			textColor: (checkoutSettings.testimonials as Record<string, unknown>)?.textColor as string || 'text-gray-800',
			borderColor: (checkoutSettings.testimonials as Record<string, unknown>)?.borderColor as string || 'border-gray-200',
		};

		// Sidebar settings
		const sidebarSettings = {
			enabled: (checkoutSettings.sidebar as Record<string, unknown>)?.enabled as boolean || false,
			bannerUrl: (checkoutSettings.sidebar as Record<string, unknown>)?.bannerUrl as string || undefined,
			title: (checkoutSettings.sidebar as Record<string, unknown>)?.title as string || '',
			content: (checkoutSettings.sidebar as Record<string, unknown>)?.content as string || '',
			backgroundColor: (checkoutSettings.sidebar as Record<string, unknown>)?.backgroundColor as string || 'bg-white',
			textColor: (checkoutSettings.sidebar as Record<string, unknown>)?.textColor as string || 'text-gray-800',
			borderColor: (checkoutSettings.sidebar as Record<string, unknown>)?.borderColor as string || 'border-gray-200',
			borderRadius: (checkoutSettings.sidebar as Record<string, unknown>)?.borderRadius as string || 'rounded-lg',
			shadow: (checkoutSettings.sidebar as Record<string, unknown>)?.shadow as boolean || true,
		};

		return (
			<div className='min-h-screen'>
				<CheckoutHeader settings={headerSettings} />
				<UrgencyBar settings={urgencySettings} />

				<div className='mx-3 md:mx-auto md:container py-5'>
					<div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
						<div className="lg:col-span-2 space-y-6">
							<CheckoutBanner settings={bannerSettings} />
							<TrustBadges settings={trustBadgesSettings} />
							<GuaranteeCards settings={guaranteeCardsSettings} />
							<ScarcityIndicator settings={scarcitySettings} />
							<Testimonials settings={testimonialsSettings} />

							<CheckoutForm
								product={{
									id: product.id,
									title: product.name,
									description: product.description,
									type: product.type === 'MENTORSHIP' ? 'MENTORING' : product.type as 'COURSE' | 'EBOOK' | 'MENTORING',
									price: Number(product.priceCents) / 100,
									installmentsLimit: 12, // Default value
									enableInstallments: true, // Default value
									thumbnail: product.thumbnail,
									checkoutType: product.checkoutType,
									acceptedPayments: ['CREDIT_CARD', 'PIX'], // Default values
									checkoutSettings: product.settings,
									customCheckoutUrl: null,
									successUrl: null,
									cancelUrl: null,
									termsUrl: null,
									offers: offers?.map((o) => ({
										id: o.id,
										title: o.name,
										description: null,
										price: Number(o.valueCents) / 100,
										type: o.type,
									})),
								}}
								// Pass conversion settings to CheckoutForm
								trustBadgesSettings={trustBadgesSettings}
								scarcitySettings={scarcitySettings}
								testimonialsSettings={testimonialsSettings}
								sidebarSettings={sidebarSettings}
								urgencySettings={urgencySettings}
								guaranteeCardsSettings={guaranteeCardsSettings}
							/>
						</div>
						<div className="lg:col-span-1">
							<CheckoutSidebar settings={sidebarSettings} />
						</div>
					</div>
				</div>
			</div>
		);
	} catch (error) {
		console.error('Error loading product for checkout:', error);
		redirect('/');
	}
}
