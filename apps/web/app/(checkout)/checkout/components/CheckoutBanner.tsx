"use client";

import { useState } from "react";

interface CheckoutBannerProps {
  bannerUrl: string | null;
  enabled?: boolean;
  maxHeight?: string;
  borderRadius?: string;
  shadow?: boolean;
}

export function CheckoutBanner({
  bannerUrl,
  enabled = true,
  maxHeight = '300px',
  borderRadius = 'rounded-lg',
  shadow = true
}: CheckoutBannerProps) {
  const [imageError, setImageError] = useState(false);

  // Don't show anything if disabled or no banner URL
  if (!enabled || !bannerUrl) {
    return null;
  }

  // Convert R2 URL to CDN URL if needed
  const displayUrl = bannerUrl.includes('r2.cloudflarestorage.com')
    ? bannerUrl.replace(
        'https://5c2abf4808262928f9012763daf2e491.r2.cloudflarestorage.com/supgateway/',
        'https://cdn.nextrusti.com/'
      )
    : bannerUrl;

  // Don't show anything if image fails to load
  if (imageError) {
    return null;
  }

  return (
    <div className="w-full mb-6 container max-w-6xl mx-auto px-0 lg:px-4">
      <img
        src={displayUrl}
        alt="Checkout banner"
        className={`w-full ${borderRadius} ${shadow ? 'shadow-sm' : ''}`}
        onError={() => setImageError(true)}
        style={{
          maxHeight,
          objectFit: 'cover',
          width: '100%'
        }}
      />
    </div>
  );
}
