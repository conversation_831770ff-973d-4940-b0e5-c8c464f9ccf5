"use client";

import { useState } from "react";

interface CheckoutBannerProps {
  settings: {
    enabled: boolean;
    url: string | null;
    maxHeight: string;
    borderRadius: string;
    shadow: boolean;
  };
}

export function CheckoutBanner({ settings }: CheckoutBannerProps) {
  const [imageError, setImageError] = useState(false);

  // Don't show anything if disabled or no banner URL
  if (!settings.enabled || !settings.url) {
    return null;
  }

  // Convert R2 URL to CDN URL if needed
  const displayUrl = settings.url.includes('r2.cloudflarestorage.com')
    ? settings.url.replace(
        'https://5c2abf4808262928f9012763daf2e491.r2.cloudflarestorage.com/supgateway/',
        'https://cdn.nextrusti.com/'
      )
    : settings.url;

  // Don't show anything if image fails to load
  if (imageError) {
    return null;
  }

  return (
    <div className="w-full mb-6">
      <img
        src={displayUrl}
        alt="Checkout banner"
        className={`w-full ${settings.borderRadius} ${settings.shadow ? 'shadow-lg' : ''}`}
        onError={() => setImageError(true)}
        style={{
          maxHeight: settings.maxHeight,
          objectFit: 'cover',
          width: '100%'
        }}
      />
    </div>
  );
}
