'use client';

import { UrgencyBar } from './urgency-bar';
import { TrustBadges } from './trust-badges';
import { ScarcityIndicator } from './scarcity-indicator';
import { TestimonialsCarousel } from './testimonials-carousel';

interface CheckoutConversionElementsProps {
  // Urgency Bar
  urgencyEnabled?: boolean;
  urgencyMessage?: string;
  urgencyEndTime?: Date;
  urgencyBackgroundColor?: string;
  urgencyTextColor?: string;
  urgencyAccentColor?: string;

  // Trust Badges
  trustBadgesEnabled?: boolean;
  trustBadges?: Array<{
    id: string;
    title: string;
    subtitle: string;
    icon: 'shield' | 'lock' | 'mail' | 'check' | 'star' | 'award' | 'truck' | 'credit-card';
    enabled: boolean;
  }>;
  trustBadgesLayout?: 'horizontal' | 'vertical' | 'grid';
  trustBadgesShowDescriptions?: boolean;
  trustBadgesBackgroundColor?: string;
  trustBadgesTextColor?: string;
  trustBadgesBorderColor?: string;

  // Scarcity
  scarcityEnabled?: boolean;
  scarcityTotalStock?: number;
  scarcitySoldCount?: number;
  scarcityMessage?: string;
  scarcityVariant?: 'warning' | 'danger' | 'info';
  scarcityShowIcon?: boolean;
  scarcityBackgroundColor?: string;
  scarcityTextColor?: string;
  scarcityBorderColor?: string;

  // Testimonials
  testimonialsEnabled?: boolean;
  testimonials?: Array<{
    id: string;
    name: string;
    rating: number;
    comment: string;
    avatar?: string;
    location?: string;
    verified?: boolean;
  }>;
  testimonialsMaxTestimonials?: number;
  testimonialsAutoPlay?: boolean;
  testimonialsAutoPlayInterval?: number;
  testimonialsShowControls?: boolean;
  testimonialsShowStars?: boolean;
  testimonialsShowAvatars?: boolean;
  testimonialsBackgroundColor?: string;
  testimonialsTextColor?: string;
  testimonialsBorderColor?: string;
}

export function CheckoutConversionElements({
  // Urgency Bar
  urgencyEnabled = false,
  urgencyMessage = "Esta oferta se encerra em:",
  urgencyEndTime,
  urgencyBackgroundColor = "bg-red-50",
  urgencyTextColor = "text-white",
  urgencyAccentColor = "bg-red-600",

  // Trust Badges
  trustBadgesEnabled = true,
  trustBadges,
  trustBadgesLayout = 'horizontal',
  trustBadgesShowDescriptions = true,
  trustBadgesBackgroundColor = 'bg-blue-50',
  trustBadgesTextColor = 'text-blue-800',
  trustBadgesBorderColor = 'border-blue-200',

  // Scarcity
  scarcityEnabled = false,
  scarcityTotalStock = 100,
  scarcitySoldCount = 0,
  scarcityMessage = "Apenas {remaining} vagas restantes!",
  scarcityVariant = 'warning',
  scarcityShowIcon = true,
  scarcityBackgroundColor,
  scarcityTextColor,
  scarcityBorderColor,

  // Testimonials
  testimonialsEnabled = true,
  testimonials,
  testimonialsMaxTestimonials = 3,
  testimonialsAutoPlay = true,
  testimonialsAutoPlayInterval = 5000,
  testimonialsShowControls = true,
  testimonialsShowStars = true,
  testimonialsShowAvatars = true,
  testimonialsBackgroundColor = 'bg-gray-50',
  testimonialsTextColor = 'text-gray-800',
  testimonialsBorderColor = 'border-gray-200',
}: CheckoutConversionElementsProps) {
  return (
    <div className="space-y-4">
      {/* Urgency Bar - Always at the top */}
      <UrgencyBar
        enabled={urgencyEnabled}
        message={urgencyMessage}
        endTime={urgencyEndTime}
        backgroundColor={urgencyBackgroundColor}
        textColor={urgencyTextColor}
        accentColor={urgencyAccentColor}
      />

      {/* Trust Badges */}
      <TrustBadges
        enabled={trustBadgesEnabled}
        badges={trustBadges}
        layout={trustBadgesLayout}
        showDescriptions={trustBadgesShowDescriptions}
        backgroundColor={trustBadgesBackgroundColor}
        textColor={trustBadgesTextColor}
        borderColor={trustBadgesBorderColor}
      />

      {/* Scarcity Indicator */}
      <ScarcityIndicator
        enabled={scarcityEnabled}
        totalStock={scarcityTotalStock}
        soldCount={scarcitySoldCount}
        message={scarcityMessage}
        variant={scarcityVariant}
        showIcon={scarcityShowIcon}
        backgroundColor={scarcityBackgroundColor}
        textColor={scarcityTextColor}
        borderColor={scarcityBorderColor}
      />

      {/* Testimonials */}
      <TestimonialsCarousel
        enabled={testimonialsEnabled}
        testimonials={testimonials}
        maxTestimonials={testimonialsMaxTestimonials}
        autoPlay={testimonialsAutoPlay}
        autoPlayInterval={testimonialsAutoPlayInterval}
        showControls={testimonialsShowControls}
        showStars={testimonialsShowStars}
        showAvatars={testimonialsShowAvatars}
        backgroundColor={testimonialsBackgroundColor}
        textColor={testimonialsTextColor}
        borderColor={testimonialsBorderColor}
      />
    </div>
  );
}
