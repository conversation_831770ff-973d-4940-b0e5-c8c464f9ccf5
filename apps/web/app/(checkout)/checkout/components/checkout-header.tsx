// components/checkout-header.tsx
import Link from 'next/link';
import Image from 'next/image';
import { Logo } from '@shared/components/Logo';

interface CheckoutHeaderProps {
  settings: {
    showLogo: boolean;
    logoUrl: string | null;
    companyName: string;
  };
}

export function CheckoutHeader({ settings }: CheckoutHeaderProps) {
	return (
		<header className='w-full  border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60'>
			<div className='container max-w-6xl mx-auto flex h-12 items-center justify-between'>
				{settings.showLogo ? (
					<Link href='/' className='flex items-center space-x-2'>
						{settings.logoUrl ? (
							<Image
								src={settings.logoUrl}
								alt={settings.companyName}
								width={32}
								height={32}
								className='h-8 w-8'
							/>
						) : (
							<Logo />
						)}
						<span className='font-semibold text-lg'>{settings.companyName}</span>
					</Link>
				) : (
					<div /> // Empty div to maintain layout
				)}

				<div className='flex items-center gap-1'>
					<div className='flex items-center justify-center rounded-[4px] bg-[#20c37418] p-1'>
						<svg
							stroke='currentColor'
							fill='currentColor'
							strokeWidth='0'
							viewBox='0 0 512 512'
							className='text-[#20c374]'
							height={20}
							width={20}
							xmlns='http://www.w3.org/2000/svg'
						>
							<path
								fill='none'
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth='32'
								d='M336 176 225.2 ***********.8'
							/>
							<path
								fill='none'
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth='32'
								d='M463.1 112.37C373.68 96.33 336.71 84.45 256 48c-80.71 36.45-117.68 48.33-207.1 64.37C32.7 369.13 240.58 457.79 256 464c15.42-6.21 223.3-94.87 207.1-351.63z'
							/>
						</svg>
					</div>
					<div>
						<div className='block text-[0.625rem] font-bold text-[#20c374]'>
							PAGAMENTO <br />100% SEGURO
						</div>
					</div>
				</div>
			</div>
		</header>
	);
}
