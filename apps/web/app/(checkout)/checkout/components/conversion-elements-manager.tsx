'use client';

import { UrgencyTimer } from '../../../../modules/ui/components/conversion/urgency-timer';
import { Scarcity } from '../../../../modules/ui/components/conversion/scarcity';
import { TrustBadges } from './trust-badges';
import { TestimonialsCards } from './testimonials-cards';
import { TestimonialsCarousel } from './testimonials-carousel';

interface ConversionElementsManagerProps {
  // Urgência
  urgency: {
    enabled: boolean;
    message: string;
    endTime?: Date;
    backgroundColor: string;
    textColor: string;
    accentColor: string;
  };

  // Confiança
  trustBadges: {
    enabled: boolean;
    badges: Array<{
      id: string;
      title: string;
      subtitle: string;
      icon: string;
      enabled: boolean;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    showDescriptions: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Escassez
  scarcity: {
    enabled: boolean;
    totalStock: number;
    soldCount: number;
    message: string;
    variant: 'warning' | 'danger' | 'info';
    showIcon: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Depoimentos
  testimonials: {
    enabled: boolean;
    testimonials: Array<{
      id: string;
      name: string;
      rating: number;
      comment: string;
      avatar?: string;
      location?: string;
      verified?: boolean;
    }>;
    maxTestimonials: number;
    autoPlay: boolean;
    autoPlayInterval: number;
    showControls: boolean;
    showStars: boolean;
    showAvatars: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Layout options
  layout?: 'sidebar' | 'inline' | 'carousel';
  className?: string;
}

export function ConversionElementsManager({
  urgency,
  trustBadges,
  scarcity,
  testimonials,
  layout = 'sidebar',
  className = ''
}: ConversionElementsManagerProps) {

  const renderUrgency = () => {
    if (!urgency.enabled) return null;

    return (
      <div className="mb-4">
        <UrgencyTimer
          endTime={urgency.endTime}
          message={urgency.message}
          variant="warning"
          className={`${urgency.backgroundColor} ${urgency.textColor} ${urgency.accentColor}`}
        />
      </div>
    );
  };

  const renderTrustBadges = () => {
    if (!trustBadges.enabled) return null;

    const enabledBadges = trustBadges.badges.filter(badge => badge.enabled);

    return (
      <div className="mb-4">
        <TrustBadges
          enabled={true}
          badges={enabledBadges}
          layout={trustBadges.layout}
          showDescriptions={trustBadges.showDescriptions}
          backgroundColor={trustBadges.backgroundColor}
          textColor={trustBadges.textColor}
          borderColor={trustBadges.borderColor}
        />
      </div>
    );
  };

  const renderScarcity = () => {
    if (!scarcity.enabled) return null;

    return (
      <div className="mb-4">
        <Scarcity
          totalStock={scarcity.totalStock}
          soldCount={scarcity.soldCount}
          message={scarcity.message}
          variant={scarcity.variant}
          showIcon={scarcity.showIcon}
          className={`${scarcity.backgroundColor} ${scarcity.textColor} ${scarcity.borderColor}`}
        />
      </div>
    );
  };

  const renderTestimonials = () => {
    if (!testimonials.enabled) return null;

    const displayTestimonials = testimonials.testimonials.slice(0, testimonials.maxTestimonials);

    if (layout === 'carousel') {
      return (
        <div className="mb-4">
          <TestimonialsCarousel
            enabled={true}
            testimonials={displayTestimonials}
            maxTestimonials={testimonials.maxTestimonials}
            autoPlay={testimonials.autoPlay}
            autoPlayInterval={testimonials.autoPlayInterval}
            showControls={testimonials.showControls}
            showStars={testimonials.showStars}
            showAvatars={testimonials.showAvatars}
            backgroundColor={testimonials.backgroundColor}
            textColor={testimonials.textColor}
            borderColor={testimonials.borderColor}
          />
        </div>
      );
    }

    return (
      <div className="mb-4">
        <TestimonialsCards
          enabled={true}
          testimonials={displayTestimonials}
          maxTestimonials={testimonials.maxTestimonials}
          showStars={testimonials.showStars}
          showAvatars={testimonials.showAvatars}
          backgroundColor={testimonials.backgroundColor}
          textColor={testimonials.textColor}
          borderColor={testimonials.borderColor}
          layout={layout === 'inline' ? 'horizontal' : 'vertical'}
        />
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Urgência - sempre no topo */}
      {renderUrgency()}

      {/* Badges de Confiança */}
      {renderTrustBadges()}

      {/* Escassez */}
      {renderScarcity()}

      {/* Depoimentos */}
      {renderTestimonials()}
    </div>
  );
}

// Componente para exibir elementos de conversão na barra de urgência (topo da página)
export function UrgencyBarElements({
  urgency,
  className = ''
}: {
  urgency: ConversionElementsManagerProps['urgency'];
  className?: string;
}) {
  if (!urgency.enabled) return null;

  return (
    <div className={`w-full ${className}`}>
      <UrgencyTimer
        endTime={urgency.endTime}
        message={urgency.message}
        variant="warning"
        className={`${urgency.backgroundColor} ${urgency.textColor} ${urgency.accentColor}`}
      />
    </div>
  );
}

// Componente para exibir elementos de conversão no resumo do checkout
export function CheckoutSummaryElements({
  trustBadges,
  scarcity,
  testimonials,
  className = ''
}: {
  trustBadges: ConversionElementsManagerProps['trustBadges'];
  scarcity: ConversionElementsManagerProps['scarcity'];
  testimonials: ConversionElementsManagerProps['testimonials'];
  className?: string;
}) {
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Badges de Confiança */}
      {trustBadges.enabled && (
        <TrustBadges
          enabled={true}
          badges={trustBadges.badges.filter(badge => badge.enabled)}
          layout={trustBadges.layout}
          showDescriptions={trustBadges.showDescriptions}
          backgroundColor={trustBadges.backgroundColor}
          textColor={trustBadges.textColor}
          borderColor={trustBadges.borderColor}
        />
      )}

      {/* Escassez */}
      {scarcity.enabled && (
        <Scarcity
          totalStock={scarcity.totalStock}
          soldCount={scarcity.soldCount}
          message={scarcity.message}
          variant={scarcity.variant}
          showIcon={scarcity.showIcon}
          className={`${scarcity.backgroundColor} ${scarcity.textColor} ${scarcity.borderColor}`}
        />
      )}

      {/* Depoimentos */}
      {testimonials.enabled && (
        <TestimonialsCards
          enabled={true}
          testimonials={testimonials.testimonials.slice(0, testimonials.maxTestimonials)}
          maxTestimonials={testimonials.maxTestimonials}
          showStars={testimonials.showStars}
          showAvatars={testimonials.showAvatars}
          backgroundColor={testimonials.backgroundColor}
          textColor={testimonials.textColor}
          borderColor={testimonials.borderColor}
          layout="vertical"
        />
      )}
    </div>
  );
}
