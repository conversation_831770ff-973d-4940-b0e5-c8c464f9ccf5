'use client';

import { useState } from 'react';
import { Check, Shield, Star, Users, Zap } from 'lucide-react';
import { Offer } from './types';
import { MockProductImage, getProductTypeFromTitle } from './mock-images';

interface EnhancedOrderBumpCardProps {
	offer: Offer;
	checked: boolean;
	onToggle: (checked: boolean) => void;
	variant?: 'default' | 'premium' | 'social';
}

export function EnhancedOrderBumpCard({
	offer,
	checked,
	onToggle,
	variant = 'default',
}: EnhancedOrderBumpCardProps) {
	const [isHovered, setIsHovered] = useState(false);

	// Mock data para diferentes tipos de ofertas - COR ÚNICA PARA NEUROMARKETING
	const getOfferData = () => {
		// Cor laranja/vermelho para neuromarketing - comprovadamente eficaz para conversão
		const neuromarketingColors = {
			backgroundColor: 'bg-gradient-to-r from-orange-50 to-red-50',
			borderColor: 'border-orange-200',
			accentColor: 'text-orange-600',
			headerBg: 'bg-gradient-to-r from-orange-100 to-red-100',
			discountBg: 'bg-red-500',
			priceColor: 'text-red-600',
		};

		switch (variant) {
			case 'premium':
				return {
					header: 'SIM, QUERO O P24H + ATUALIZAÇÕES PRA SEMPRE',
					icon: <Shield className="w-6 h-6 text-orange-500" />,
					badge: 'ACESSO VITALÍCIO',
					title: 'Acesso Permanente + Atualizações Futuras',
					description: 'Tenha acesso para sempre ao P24H + suas atualizações',
					originalPrice: 97.00,
					discount: 90,
					discountText: '90% OFF',
					...neuromarketingColors,
				};
			case 'social':
				return {
					header: 'SIM, EU ACEITO ESSA OFERTA ESPECIAL!',
					icon: <Users className="w-6 h-6 text-orange-500" />,
					badge: 'CRESCIMENTO GARANTIDO',
					title: 'DE 150 A 300 SEGUIDORES POR SEMANA - 81% OFF:',
					description: 'Adicionar a compra a Estratégia para crescer seguidores toda semana, sem viralizar, sem trends e sem precisar postar conteúdo todo dia.',
					originalPrice: 297.00,
					discount: 81,
					discountText: '81% OFF',
					...neuromarketingColors,
				};
			default:
				return {
					header: 'APROVEITE ESTA OFERTA ESPECIAL!',
					icon: <Zap className="w-6 h-6 text-orange-500" />,
					badge: 'OFERTA LIMITADA',
					title: offer.title || 'Oferta Especial',
					description: offer.description || 'Não perca esta oportunidade única!',
					originalPrice: offer.price * 2, // Simular preço original
					discount: 50,
					discountText: '50% OFF',
					...neuromarketingColors,
				};
		}
	};

	const offerData = getOfferData();
	const finalPrice = offer.price;

	const handleCardClick = () => {
		onToggle(!checked);
	};

	return (
		<div
			className={`
				relative border-2 border-dashed ${offerData.borderColor} rounded-lg overflow-hidden
				transition-all duration-300 ease-in-out cursor-pointer
				${isHovered ? 'shadow-lg scale-[1.02]' : 'shadow-sm'}
				${checked ? 'ring-2 ring-orange-500 ring-opacity-50 bg-orange-50/30' : 'bg-white'}
			`}
			onMouseEnter={() => setIsHovered(true)}
			onMouseLeave={() => setIsHovered(false)}
			onClick={handleCardClick}
		>
			{/* Header compacto com cor neuromarketing */}
			<div className={`${offerData.headerBg} px-3 py-2 text-center`}>
				<h3 className={`font-bold text-xs ${offerData.accentColor} uppercase tracking-wide`}>
					{offerData.header}
				</h3>
			</div>

			{/* Conteúdo principal - Layout compacto */}
			<div className="p-3">
				<div className="flex items-center gap-3">
					{/* Imagem mock do produto à esquerda */}
					<div className="flex-shrink-0 relative">
						<MockProductImage
							type={getProductTypeFromTitle(offerData.title)}
							className="w-12 h-12"
						/>
					</div>

					{/* Informações do produto - centro */}
					<div className="flex-1 min-w-0">
						{/* Badge */}
						<div className={`inline-block px-2 py-0.5 rounded-full text-xs font-bold ${offerData.accentColor} bg-opacity-20 ${offerData.backgroundColor} mb-1`}>
							{offerData.badge}
						</div>

						{/* Título */}
						<h4 className="font-bold text-sm text-gray-900 mb-1 leading-tight line-clamp-2">
							{offerData.title}
						</h4>

						{/* Descrição compacta */}
						<p className="text-xs text-gray-600 leading-relaxed line-clamp-2">
							{offerData.description}
						</p>
					</div>

					{/* Preços e badge de desconto à direita */}
					<div className="flex-shrink-0 text-right">
						<div className="space-y-1">
							{/* Preço original riscado */}
							<div className="text-xs text-gray-400 line-through">
								R$ {offerData.originalPrice.toFixed(2)}
							</div>

							{/* Preço final com cor neuromarketing */}
							<div className={`text-lg font-bold ${offerData.priceColor}`}>
								R$ {finalPrice.toFixed(2)}
							</div>

							{/* Badge de desconto próximo aos valores */}
							<div className={`${offerData.discountBg} text-white text-xs px-2 py-0.5 rounded font-bold`}>
								{offerData.discount}% OFF
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Footer compacto com checkbox */}
			<div className={`${offerData.backgroundColor} px-3 py-2 border-t ${offerData.borderColor}`}>
				<div className="flex items-center gap-2">
					{/* Checkbox visual com cor neuromarketing */}
					<div className={`
						w-5 h-5 rounded-full flex items-center justify-center
						transition-all duration-200
						${checked ? 'bg-orange-500 text-white' : 'bg-gray-200 text-gray-500'}
					`}>
						{checked && <Check className="w-3 h-3" />}
					</div>

					{/* Texto do checkbox */}
					<span className={`font-medium text-xs ${checked ? offerData.accentColor : 'text-gray-600'}`}>
						{checked ? '✓ Adicionado ao pedido' : 'Clique para adicionar'}
					</span>
				</div>
			</div>

			{/* Efeito de hover com cor neuromarketing */}
			{isHovered && (
				<div className="absolute inset-0 pointer-events-none">
					<div className={`
						absolute inset-0 rounded-lg
						bg-gradient-to-r from-orange-500/5 to-red-500/5
					`} />
				</div>
			)}
		</div>
	);
}
