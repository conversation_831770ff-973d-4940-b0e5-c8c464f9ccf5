'use client';

import { useState, useEffect } from 'react';
import { Users, AlertTriangle, Clock } from 'lucide-react';

interface ScarcityIndicatorProps {
  enabled?: boolean;
  totalStock?: number;
  soldCount?: number;
  message?: string;
  variant?: 'warning' | 'danger' | 'info';
  showIcon?: boolean;
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
}

export function ScarcityIndicator({
  enabled = true,
  totalStock = 100,
  soldCount = 0,
  message = "Apenas {remaining} vagas restantes!",
  variant = 'warning',
  showIcon = true,
  backgroundColor,
  textColor,
  borderColor
}: ScarcityIndicatorProps) {
  const [remaining, setRemaining] = useState(totalStock - soldCount);

  useEffect(() => {
    setRemaining(totalStock - soldCount);
  }, [totalStock, soldCount]);

  if (!enabled || remaining <= 0) return null;

  const getVariantClasses = () => {
    switch (variant) {
      case 'danger':
        return {
          bg: backgroundColor || 'bg-red-50',
          text: textColor || 'text-red-800',
          border: borderColor || 'border-red-200',
          icon: 'text-red-600'
        };
      case 'info':
        return {
          bg: backgroundColor || 'bg-blue-50',
          text: textColor || 'text-blue-800',
          border: borderColor || 'border-blue-200',
          icon: 'text-blue-600'
        };
      case 'warning':
      default:
        return {
          bg: backgroundColor || 'bg-orange-50',
          text: textColor || 'text-orange-800',
          border: borderColor || 'border-orange-200',
          icon: 'text-orange-600'
        };
    }
  };

  const variantClasses = getVariantClasses();
  const percentageLeft = Math.round((remaining / totalStock) * 100);

  const getIcon = () => {
    if (remaining <= 5) return AlertTriangle;
    if (remaining <= 20) return Clock;
    return Users;
  };

  const IconComponent = getIcon();

  return (
    <div className={`${variantClasses.bg} ${variantClasses.border} border rounded-lg p-4`}>
      <div className="flex items-center gap-3">
        {showIcon && (
          <div className={`flex-shrink-0 ${variantClasses.icon}`}>
            <IconComponent className="h-5 w-5" />
          </div>
        )}
        <div className="flex-1">
          <div className={`font-semibold text-sm ${variantClasses.text}`}>
            {message.replace('{remaining}', remaining.toString())}
          </div>
          <div className="mt-2">
            <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
              <span>Estoque restante</span>
              <span>{percentageLeft}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-500 ${
                  remaining <= 5 ? 'bg-red-500' : remaining <= 20 ? 'bg-orange-500' : 'bg-green-500'
                }`}
                style={{ width: `${percentageLeft}%` }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
