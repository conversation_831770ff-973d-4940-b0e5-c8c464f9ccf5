'use client';

import { useState } from 'react';
import Image from 'next/image';

interface SidebarBannerProps {
  enabled?: boolean;
  bannerUrl?: string | null;
  title?: string;
  content?: string;
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
  borderRadius?: string;
  shadow?: boolean;
}

export function SidebarBanner({
  enabled = false,
  bannerUrl,
  title = "Informações Importantes",
  content = "Adicione informações úteis para seus clientes aqui.",
  backgroundColor = 'bg-blue-50',
  textColor = 'text-blue-800',
  borderColor = 'border-blue-200',
  borderRadius = 'rounded-lg',
  shadow = true
}: SidebarBannerProps) {
  const [imageError, setImageError] = useState(false);

  if (!enabled) return null;

  // Convert R2 URL to CDN URL if needed
  const displayUrl = bannerUrl && bannerUrl.includes('r2.cloudflarestorage.com')
    ? bannerUrl.replace(
        'https://5c2abf4808262928f9012763daf2e491.r2.cloudflarestorage.com/supgateway/',
        'https://cdn.nextrusti.com/'
      )
    : bannerUrl;

  // Se não tem banner URL, não mostra nada
  if (!bannerUrl || imageError) {
    return null;
  }

  return (
    <img
      src={displayUrl}
      alt="Sidebar banner"
      className="w-full h-auto object-cover rounded-lg mb-4"
      onError={() => setImageError(true)}
      style={{
        width: '100%',
        height: 'auto',
        objectFit: 'cover'
      }}
    />
  );
}
