'use client';

import { CheckCircle } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/avatar';
import { Badge } from '@ui/components/badge';
import { cn } from '@ui/lib';

interface Testimonial {
  id: string;
  name: string;
  rating: number;
  comment: string;
  avatar?: string;
  location?: string;
  verified?: boolean;
  company?: string;
  position?: string;
  source?: 'tiktok' | 'whatsapp' | 'instagram' | 'facebook' | 'youtube' | 'email' | 'website' | 'other';
  sourceUrl?: string;
}

interface SimpleTestimonialsProps {
  testimonials: Testimonial[];
  maxTestimonials?: number;
  showStars?: boolean;
  showAvatars?: boolean;
  showSource?: boolean;
  showVerified?: boolean;
  className?: string;
}

const sourceIcons = {
  tiktok: '🎵',
  whatsapp: '💬',
  instagram: '📷',
  facebook: '👥',
  youtube: '📺',
  email: '📧',
  website: '🌐',
  other: '💬'
};

const sourceLabels = {
  tiktok: 'TikTok',
  whatsapp: 'WhatsApp',
  instagram: 'Instagram',
  facebook: 'Facebook',
  youtube: 'YouTube',
  email: 'Email',
  website: 'Website',
  other: 'Outro'
};

export function SimpleTestimonials({
  testimonials = [],
  maxTestimonials = 6,
  showStars = true,
  showAvatars = true,
  showSource = true,
  showVerified = true,
  className = ''
}: SimpleTestimonialsProps) {
  const displayTestimonials = testimonials.slice(0, maxTestimonials);

  if (displayTestimonials.length === 0) {
    return null;
  }

  return (
    <div className={cn('w-full space-y-4', className)}>
      {displayTestimonials.map((testimonial) => (
        <div
          key={testimonial.id}
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-sm transition-shadow"
        >
          {/* Header do Card */}
          <div className="flex items-center gap-3 mb-3">
            {showAvatars && (
              <Avatar className="h-10 w-10 rounded-full">
                <AvatarImage
                  src={testimonial.avatar}
                  alt={testimonial.name}
                  className="rounded-full"
                />
                <AvatarFallback className="bg-blue-100 text-blue-600 font-medium rounded-full">
                  {testimonial.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
            )}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 text-sm">
                  {testimonial.name}
                </h4>
                {showVerified && testimonial.verified && (
                  <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
                )}
              </div>
              {showSource && testimonial.source && (
                <div className="mt-1">
                  <Badge
                    variant="outline"
                    className="text-xs px-2 py-1 bg-gray-50 dark:bg-gray-700"
                  >
                    <span className="mr-1">
                      {sourceIcons[testimonial.source]}
                    </span>
                    {sourceLabels[testimonial.source]}
                  </Badge>
                </div>
              )}
            </div>
          </div>

          {/* Comment */}
          <blockquote className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
            "{testimonial.comment}"
          </blockquote>
        </div>
      ))}
    </div>
  );
}
