'use client';

import { Star, CheckCircle } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/avatar';
import { Badge } from '@ui/components/badge';
import { cn } from '@ui/lib';

interface Testimonial {
  id: string;
  name: string;
  rating: number;
  comment: string;
  avatar?: string;
  location?: string;
  verified?: boolean;
  company?: string;
  position?: string;
  source?: 'tiktok' | 'whatsapp' | 'instagram' | 'facebook' | 'youtube' | 'email' | 'website' | 'other';
  sourceUrl?: string;
}

interface TestimonialsCardsGridProps {
  testimonials: Testimonial[];
  maxTestimonials?: number;
  showStars?: boolean;
  showAvatars?: boolean;
  showSource?: boolean;
  showVerified?: boolean;
  className?: string;
}

const sourceIcons = {
  tiktok: '🎵',
  whatsapp: '💬',
  instagram: '📷',
  facebook: '👥',
  youtube: '📺',
  email: '📧',
  website: '🌐',
  other: '💬'
};

const sourceLabels = {
  tiktok: 'TikTok',
  whatsapp: 'WhatsApp',
  instagram: 'Instagram',
  facebook: 'Facebook',
  youtube: 'YouTube',
  email: 'Email',
  website: 'Website',
  other: 'Outro'
};

export function TestimonialsCardsGrid({
  testimonials = [],
  maxTestimonials = 6,
  showStars = true,
  showAvatars = true,
  showSource = true,
  showVerified = true,
  className = ''
}: TestimonialsCardsGridProps) {
  const displayTestimonials = testimonials.slice(0, maxTestimonials);

  if (displayTestimonials.length === 0) {
    return null;
  }

  return (
    <div className={cn('w-full', className)}>
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          O que nossos clientes dizem
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {displayTestimonials.length} depoimentos verificados
        </p>
      </div>

      {/* Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {displayTestimonials.map((testimonial) => (
          <div
            key={testimonial.id}
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow"
          >
            {/* Header do Card */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3">
                {showAvatars && (
                  <Avatar className="h-10 w-10">
                    <AvatarImage
                      src={testimonial.avatar}
                      alt={testimonial.name}
                    />
                    <AvatarFallback className="bg-blue-100 text-blue-600 font-medium">
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                )}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 text-sm truncate">
                      {testimonial.name}
                    </h4>
                    {showVerified && testimonial.verified && (
                      <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
                    )}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 truncate">
                    {testimonial.position && `${testimonial.position}`}
                    {testimonial.company && ` na ${testimonial.company}`}
                    {testimonial.location && (
                      <span> • {testimonial.location}</span>
                    )}
                  </div>
                </div>
              </div>

              {/* Source Badge */}
              {showSource && testimonial.source && (
                <Badge
                  variant="outline"
                  className="text-xs px-2 py-1 bg-gray-50 dark:bg-gray-700"
                >
                  <span className="mr-1">
                    {sourceIcons[testimonial.source]}
                  </span>
                  {sourceLabels[testimonial.source]}
                </Badge>
              )}
            </div>

            {/* Rating */}
            {showStars && (
              <div className="flex items-center gap-1 mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={cn(
                      'h-3 w-3',
                      i < testimonial.rating
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    )}
                  />
                ))}
                <span className="text-xs text-gray-500 ml-1">
                  {testimonial.rating}/5
                </span>
              </div>
            )}

            {/* Comment */}
            <blockquote className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
              "{testimonial.comment}"
            </blockquote>

            {/* Footer com Source URL se disponível */}
            {showSource && testimonial.sourceUrl && (
              <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                <a
                  href={testimonial.sourceUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1"
                >
                  <span>{sourceIcons[testimonial.source]}</span>
                  Ver no {sourceLabels[testimonial.source]}
                </a>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Trust Indicators */}
      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-center gap-6 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center gap-1">
            <CheckCircle className="h-4 w-4" />
            <span>100% Seguro</span>
          </div>
          <div className="flex items-center gap-1">
            <CheckCircle className="h-4 w-4" />
            <span>Garantia de 30 dias</span>
          </div>
          <div className="flex items-center gap-1">
            <Star className="h-4 w-4 text-yellow-400 fill-current" />
            <span>4.9/5 avaliação</span>
          </div>
        </div>
      </div>
    </div>
  );
}
