'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';

interface TestimonialsProps {
  settings: {
    enabled: boolean;
    testimonials: Array<{
      id: string;
      name: string;
      rating: number;
      comment: string;
      avatar?: string;
      location?: string;
      verified?: boolean;
    }>;
    maxTestimonials: number;
    autoPlay: boolean;
    autoPlayInterval: number;
    showControls: boolean;
    showStars: boolean;
    showAvatars: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
}

export function Testimonials({ settings }: TestimonialsProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  if (!settings.enabled) return null;

  const displayTestimonials = settings.testimonials.slice(0, settings.maxTestimonials);
  
  if (displayTestimonials.length === 0) return null;

  // Auto-play functionality
  useEffect(() => {
    if (!settings.autoPlay || displayTestimonials.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % displayTestimonials.length);
    }, settings.autoPlayInterval * 1000);

    return () => clearInterval(interval);
  }, [settings.autoPlay, settings.autoPlayInterval, displayTestimonials.length]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % displayTestimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + displayTestimonials.length) % displayTestimonials.length);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
      />
    ));
  };

  const currentTestimonial = displayTestimonials[currentIndex];

  return (
    <div className="w-full mb-6">
      <div className={`relative p-6 rounded-lg border ${settings.backgroundColor} ${settings.borderColor} ${settings.textColor}`}>
        {/* Navigation Controls */}
        {settings.showControls && displayTestimonials.length > 1 && (
          <>
            <button
              onClick={prevTestimonial}
              className="absolute left-2 top-1/2 transform -translate-y-1/2 p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <button
              onClick={nextTestimonial}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </>
        )}

        {/* Testimonial Content */}
        <div className="flex flex-col items-center text-center space-y-4">
          {/* Avatar */}
          {settings.showAvatars && currentTestimonial.avatar && (
            <div className="w-16 h-16 rounded-full overflow-hidden">
              <img
                src={currentTestimonial.avatar}
                alt={currentTestimonial.name}
                className="w-full h-full object-cover"
              />
            </div>
          )}

          {/* Stars */}
          {settings.showStars && (
            <div className="flex items-center gap-1">
              {renderStars(currentTestimonial.rating)}
            </div>
          )}

          {/* Comment */}
          <blockquote className="text-lg italic">
            "{currentTestimonial.comment}"
          </blockquote>

          {/* Author Info */}
          <div className="space-y-1">
            <div className="font-semibold">
              {currentTestimonial.name}
              {currentTestimonial.verified && (
                <span className="ml-2 text-green-600 text-sm">✓ Verificado</span>
              )}
            </div>
            {currentTestimonial.location && (
              <div className="text-sm opacity-75">
                {currentTestimonial.location}
              </div>
            )}
          </div>
        </div>

        {/* Dots Indicator */}
        {displayTestimonials.length > 1 && (
          <div className="flex justify-center mt-6 space-x-2">
            {displayTestimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentIndex ? 'bg-blue-600' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
