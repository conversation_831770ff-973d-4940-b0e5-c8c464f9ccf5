'use client';

import { Shield, Lock, Mail, CheckCircle, Star, Award, Truck, CreditCard } from 'lucide-react';

interface TrustBadge {
  id: string;
  title: string;
  subtitle: string;
  icon: 'shield' | 'lock' | 'mail' | 'check' | 'star' | 'award' | 'truck' | 'credit-card';
  enabled: boolean;
}

interface TrustBadgesProps {
  enabled?: boolean;
  badges?: TrustBadge[];
  layout?: 'horizontal' | 'vertical' | 'grid';
  showDescriptions?: boolean;
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
}

const defaultBadges: TrustBadge[] = [
  {
    id: 'privacy',
    title: 'Privacidade',
    subtitle: 'Sua informação 100% segura',
    icon: 'star',
    enabled: true
  },
  {
    id: 'secure-purchase',
    title: 'Compra segura',
    subtitle: 'Ambiente seguro e autenticado',
    icon: 'shield',
    enabled: true
  },
  {
    id: 'email-delivery',
    title: 'Entregue via E-mail',
    subtitle: 'Acesso ao produto entregue por email',
    icon: 'mail',
    enabled: true
  },
  {
    id: 'approved-content',
    title: 'Conteúdo aprovado',
    subtitle: '100% revisado e aprovado',
    icon: 'check',
    enabled: true
  }
];

const iconMap = {
  shield: Shield,
  lock: Lock,
  mail: Mail,
  check: CheckCircle,
  star: Star,
  award: Award,
  truck: Truck,
  'credit-card': CreditCard
};

export function TrustBadges({
  enabled = true,
  badges = defaultBadges,
  layout = 'horizontal',
  showDescriptions = true,
  backgroundColor = 'bg-blue-50',
  textColor = 'text-blue-800',
  borderColor = 'border-blue-200'
}: TrustBadgesProps) {
  if (!enabled) return null;

  const enabledBadges = badges.filter(badge => badge.enabled);

  if (enabledBadges.length === 0) return null;

  const getLayoutClasses = () => {
    switch (layout) {
      case 'vertical':
        return 'flex flex-col space-y-3';
      case 'grid':
        return 'grid grid-cols-2 gap-3';
      case 'horizontal':
      default:
        return 'flex flex-wrap gap-3 justify-center';
    }
  };

  return (
    <div className="w-full py-4">
      <div className={`${getLayoutClasses()}`}>
        {enabledBadges.map((badge) => {
          const IconComponent = iconMap[badge.icon];

          return (
            <div
              key={badge.id}
              className={`flex items-center gap-3 ${backgroundColor} ${borderColor} border rounded-lg px-4 py-3 min-w-0 flex-1`}
            >
              <div className={`flex-shrink-0 ${textColor}`}>
                <IconComponent className="h-5 w-5" />
              </div>
              <div className="min-w-0 flex-1">
                <div className={`font-semibold text-sm ${textColor}`}>
                  {badge.title}
                </div>
                {showDescriptions && (
                  <div className={`text-xs ${textColor} opacity-80`}>
                    {badge.subtitle}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
