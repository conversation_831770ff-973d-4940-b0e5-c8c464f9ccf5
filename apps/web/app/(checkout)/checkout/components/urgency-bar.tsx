'use client';

import { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';

interface UrgencyBarProps {
  settings: {
    enabled: boolean;
    message: string;
    endTime?: Date;
    backgroundColor: string;
    textColor: string;
    accentColor: string;
  };
}

export function UrgencyBar({ settings }: UrgencyBarProps) {
  const [timeLeft, setTimeLeft] = useState<string>('');

  useEffect(() => {
    if (!settings.enabled || !settings.endTime) return;

    const updateTimer = () => {
      const now = new Date().getTime();
      const targetTime = new Date(settings.endTime).getTime();
      const difference = targetTime - now;

      if (difference > 0) {
        const hours = Math.floor(difference / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        setTimeLeft(formattedTime);
      } else {
        setTimeLeft('00:00:00');
      }
    };

    updateTimer();
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [settings.enabled, settings.endTime]);

  if (!settings.enabled) return null;

  return (
    <div className={`w-full ${settings.backgroundColor} border-t border-b border-gray-200`}>
      <div className="container max-w-6xl mx-auto px-0 lg:px-4">
        <div className="flex items-center justify-center py-3">
          <div className="flex items-center gap-3">
            <div className={`w-1 h-8 ${settings.accentColor} rounded-full`} />
            <div className="flex items-center gap-2">
              <span className={`text-sm font-medium ${settings.textColor}`}>
                {settings.message}
              </span>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span className={`text-lg font-bold ${settings.textColor}`}>
                  {timeLeft}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
