'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Switch } from '@ui/components/switch';
import { Label } from '@ui/components/label';
import { Input } from '@ui/components/input';
import { Textarea } from '@ui/components/textarea';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@ui/components/tabs';
import { Badge } from '@ui/components/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';
import { Eye, Save, Settings, Upload, X } from 'lucide-react';
import { BannerUpload } from '@saas/products/components/BannerUpload';
import { TestimonialsEditor } from './TestimonialsEditor';
import { GuaranteeCardsEditor } from './GuaranteeCardsEditor';

interface UnifiedCheckoutSettingsProps {
  productId: string;
  organizationId: string;
  currentBanner?: string | null;
  onBannerChange?: (bannerUrl: string | null) => void;
  initialSettings?: any;
  onSave?: (settings: any) => void;
  onPreview?: () => void;
}

interface CheckoutSettingsData {
  // Banner (usando upload, sem URL manual)
  banner: {
    enabled: boolean;
    maxHeight: string;
    borderRadius: string;
    shadow: boolean;
  };

  // Header
  header: {
    showLogo: boolean;
    logoUrl?: string;
    companyName: string;
  };

  // Urgência
  urgency: {
    enabled: boolean;
    message: string;
    endTime?: string;
    backgroundColor: string;
    textColor: string;
    accentColor: string;
  };

  // Confiança
  trustBadges: {
    enabled: boolean;
    badges: Array<{
      id: string;
      title: string;
      subtitle: string;
      icon: string;
      enabled: boolean;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    showDescriptions: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Cards de Garantia
  guaranteeCards: {
    enabled: boolean;
    cards: Array<{
      id: string;
      title: string;
      description: string;
      icon: string;
      customIcon?: string;
      enabled: boolean;
      order: number;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Escassez
  scarcity: {
    enabled: boolean;
    totalStock: number;
    soldCount: number;
    message: string;
    variant: 'warning' | 'danger' | 'info';
    showIcon: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Depoimentos
  testimonials: {
    enabled: boolean;
    testimonials: Array<{
      id: string;
      name: string;
      rating: number;
      comment: string;
      avatar?: string;
      location?: string;
      verified?: boolean;
    }>;
    maxTestimonials: number;
    autoPlay: boolean;
    autoPlayInterval: number;
    showControls: boolean;
    showStars: boolean;
    showAvatars: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Sidebar
  sidebar: {
    enabled: boolean;
    bannerUrl?: string;
    title: string;
    content: string;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
    borderRadius: string;
    shadow: boolean;
  };
}

const defaultSettings: CheckoutSettingsData = {
  banner: {
    enabled: true,
    maxHeight: '300px',
    borderRadius: 'rounded-lg',
    shadow: true,
  },
  header: {
    showLogo: false,
    companyName: 'SupGateway',
  },
  urgency: {
    enabled: false,
    message: 'Esta oferta se encerra em:',
    backgroundColor: 'bg-red-50',
    textColor: 'text-white',
    accentColor: 'bg-red-600',
  },
  trustBadges: {
    enabled: true,
    badges: [
      {
        id: 'security',
        title: '100% Seguro',
        subtitle: 'Pagamentos protegidos',
        icon: 'shield',
        enabled: true,
      },
      {
        id: 'guarantee',
        title: 'Garantia de 30 dias',
        subtitle: 'Devolução garantida',
        icon: 'check',
        enabled: true,
      },
      {
        id: 'support',
        title: 'Suporte 24/7',
        subtitle: 'Atendimento completo',
        icon: 'mail',
        enabled: true,
      },
    ],
    layout: 'vertical',
    showDescriptions: true,
    backgroundColor: 'bg-blue-50',
    textColor: 'text-blue-800',
    borderColor: 'border-blue-200',
  },

  guaranteeCards: {
    enabled: true,
    cards: [
      {
        id: 'security',
        title: '100% Seguro',
        description: 'Pagamentos protegidos com criptografia de ponta a ponta',
        icon: 'shield',
        enabled: true,
        order: 1,
      },
      {
        id: 'guarantee',
        title: 'Garantia de 30 dias',
        description: 'Devolução garantida se não ficar satisfeito',
        icon: 'check',
        enabled: true,
        order: 2,
      },
      {
        id: 'support',
        title: 'Suporte 24/7',
        description: 'Atendimento completo sempre disponível',
        icon: 'heart',
        enabled: true,
        order: 3,
      },
    ],
    layout: 'grid',
    backgroundColor: 'bg-green-50',
    textColor: 'text-green-800',
    borderColor: 'border-green-200',
  },
  scarcity: {
    enabled: false,
    totalStock: 100,
    soldCount: 0,
    message: 'Apenas {remaining} vagas restantes!',
    variant: 'warning',
    showIcon: true,
    backgroundColor: 'bg-orange-50',
    textColor: 'text-orange-800',
    borderColor: 'border-orange-200',
  },
  testimonials: {
    enabled: true,
    testimonials: [
      {
        id: '1',
        name: 'Carlos Silva',
        rating: 5,
        comment: 'A integração foi surpreendentemente simples. Em menos de 2 horas já estávamos processando pagamentos PIX.',
        location: 'São Paulo, SP',
        verified: true,
      },
      {
        id: '2',
        name: 'Ana Rodrigues',
        rating: 5,
        comment: 'Desde que implementamos a solução, nossa taxa de abandono de carrinho diminuiu drasticamente.',
        location: 'Rio de Janeiro, RJ',
        verified: true,
      },
    ],
    maxTestimonials: 3,
    autoPlay: true,
    autoPlayInterval: 5000,
    showControls: true,
    showStars: true,
    showAvatars: true,
    backgroundColor: 'bg-gray-50',
    textColor: 'text-gray-800',
    borderColor: 'border-gray-200',
  },
  sidebar: {
    enabled: false,
    title: 'Informações Importantes',
    content: 'Adicione informações úteis para seus clientes aqui.',
    backgroundColor: 'bg-blue-50',
    textColor: 'text-blue-800',
    borderColor: 'border-blue-200',
    borderRadius: 'rounded-lg',
    shadow: true,
  },
};

export function UnifiedCheckoutSettings({
  productId,
  organizationId,
  currentBanner,
  onBannerChange,
  initialSettings = {},
  onSave,
  onPreview
}: UnifiedCheckoutSettingsProps) {
  const [settings, setSettings] = useState<CheckoutSettingsData>({
    ...defaultSettings,
    ...initialSettings
  });
  const [activeTab, setActiveTab] = useState('banner');

  const updateSetting = (section: keyof CheckoutSettingsData, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value,
      },
    }));
  };

  const handleSave = () => {
    onSave?.(settings);
  };

  const handlePreview = () => {
    onPreview?.();
  };

  const getActiveElementsCount = () => {
    let count = 0;
    if (settings.banner.enabled && currentBanner) count++;
    if (settings.header.showLogo) count++;
    if (settings.urgency.enabled) count++;
    if (settings.guaranteeCards.enabled) count++;
    if (settings.scarcity.enabled) count++;
    if (settings.testimonials.enabled) count++;
    if (settings.sidebar.enabled) count++;
    return count;
  };

  return (
    <div className="space-y-6">
      {/* Header com contador de elementos ativos */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">Configurações do Checkout</h2>
          <p className="text-sm text-muted-foreground">
            Personalize a aparência e elementos de conversão
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {getActiveElementsCount()} elementos ativos
          </Badge>
          <Button variant="outline" onClick={handlePreview}>
            <Eye className="h-4 w-4 mr-2" />
            Visualizar
          </Button>
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            Salvar
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="banner">Banner</TabsTrigger>
          <TabsTrigger value="header">Header</TabsTrigger>
          <TabsTrigger value="urgency">Urgência</TabsTrigger>
          <TabsTrigger value="guarantee">Garantia</TabsTrigger>
          <TabsTrigger value="scarcity">Escassez</TabsTrigger>
          <TabsTrigger value="testimonials">Depoimentos</TabsTrigger>
          <TabsTrigger value="sidebar">Sidebar</TabsTrigger>
        </TabsList>

        {/* Banner Settings - Usando upload, sem URL manual */}
        <TabsContent value="banner">
          <Card>
            <CardHeader>
              <CardTitle>Banner do Checkout</CardTitle>
              <CardDescription>
                Configure o banner principal do checkout usando o upload de imagem
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Upload do Banner */}
              <div className="space-y-4">
                <Label>Imagem do Banner</Label>
                <BannerUpload
                  currentBanner={currentBanner}
                  onBannerChange={onBannerChange}
                  showLabel={false}
                />
                {currentBanner && (
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Banner configurado com sucesso
                  </div>
                )}
              </div>

              {/* Configurações de exibição */}
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="banner-enabled">Exibir Banner</Label>
                  <p className="text-sm text-muted-foreground">
                    {currentBanner ? 'Banner disponível para exibição' : 'Faça upload de uma imagem primeiro'}
                  </p>
                </div>
                <Switch
                  id="banner-enabled"
                  checked={settings.banner.enabled && !!currentBanner}
                  onCheckedChange={(checked) => updateSetting('banner', 'enabled', checked)}
                  disabled={!currentBanner}
                />
              </div>

              {settings.banner.enabled && currentBanner && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="banner-height">Altura Máxima</Label>
                      <Input
                        id="banner-height"
                        value={settings.banner.maxHeight}
                        onChange={(e) => updateSetting('banner', 'maxHeight', e.target.value)}
                        placeholder="300px"
                      />
                    </div>
                    <div>
                      <Label htmlFor="banner-radius">Bordas</Label>
                      <Select
                        value={settings.banner.borderRadius}
                        onValueChange={(value) => updateSetting('banner', 'borderRadius', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="rounded-none">Nenhuma</SelectItem>
                          <SelectItem value="rounded-sm">Pequena</SelectItem>
                          <SelectItem value="rounded">Média</SelectItem>
                          <SelectItem value="rounded-lg">Grande</SelectItem>
                          <SelectItem value="rounded-xl">Extra Grande</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="banner-shadow">Sombra</Label>
                    <Switch
                      id="banner-shadow"
                      checked={settings.banner.shadow}
                      onCheckedChange={(checked) => updateSetting('banner', 'shadow', checked)}
                    />
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Header Settings */}
        <TabsContent value="header">
          <Card>
            <CardHeader>
              <CardTitle>Configurações do Header</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="header-logo">Exibir Logo</Label>
                <Switch
                  id="header-logo"
                  checked={settings.header.showLogo}
                  onCheckedChange={(checked) => updateSetting('header', 'showLogo', checked)}
                />
              </div>

              {settings.header.showLogo && (
                <div>
                  <Label htmlFor="header-logo-url">URL do Logo</Label>
                  <Input
                    id="header-logo-url"
                    value={settings.header.logoUrl || ''}
                    onChange={(e) => updateSetting('header', 'logoUrl', e.target.value)}
                    placeholder="https://exemplo.com/logo.png"
                  />
                </div>
              )}

              <div>
                <Label htmlFor="header-company">Nome da Empresa</Label>
                <Input
                  id="header-company"
                  value={settings.header.companyName}
                  onChange={(e) => updateSetting('header', 'companyName', e.target.value)}
                  placeholder="Sua Empresa"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Urgency Settings */}
        <TabsContent value="urgency">
          <Card>
            <CardHeader>
              <CardTitle>Configurações de Urgência</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="urgency-enabled">Exibir Elementos de Urgência</Label>
                <Switch
                  id="urgency-enabled"
                  checked={settings.urgency.enabled}
                  onCheckedChange={(checked) => updateSetting('urgency', 'enabled', checked)}
                />
              </div>

              {settings.urgency.enabled && (
                <>
                  <div>
                    <Label htmlFor="urgency-message">Mensagem</Label>
                    <Input
                      id="urgency-message"
                      value={settings.urgency.message}
                      onChange={(e) => updateSetting('urgency', 'message', e.target.value)}
                      placeholder="Esta oferta se encerra em:"
                    />
                  </div>

                  <div>
                    <Label htmlFor="urgency-time">Data/Hora de Término</Label>
                    <Input
                      id="urgency-time"
                      type="datetime-local"
                      value={settings.urgency.endTime || ''}
                      onChange={(e) => updateSetting('urgency', 'endTime', e.target.value)}
                    />
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="urgency-bg">Cor de Fundo</Label>
                      <Select
                        value={settings.urgency.backgroundColor}
                        onValueChange={(value) => updateSetting('urgency', 'backgroundColor', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="bg-red-50">Vermelho Claro</SelectItem>
                          <SelectItem value="bg-orange-50">Laranja Claro</SelectItem>
                          <SelectItem value="bg-yellow-50">Amarelo Claro</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="urgency-text">Cor do Texto</Label>
                      <Select
                        value={settings.urgency.textColor}
                        onValueChange={(value) => updateSetting('urgency', 'textColor', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="text-white">Branco</SelectItem>
                          <SelectItem value="text-red-800">Vermelho Escuro</SelectItem>
                          <SelectItem value="text-orange-800">Laranja Escuro</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="urgency-accent">Cor de Destaque</Label>
                      <Select
                        value={settings.urgency.accentColor}
                        onValueChange={(value) => updateSetting('urgency', 'accentColor', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="bg-red-600">Vermelho</SelectItem>
                          <SelectItem value="bg-orange-600">Laranja</SelectItem>
                          <SelectItem value="bg-yellow-600">Amarelo</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Trust Badges Settings */}
        {/* Guarantee Cards Settings - Unificado com Confiança */}
        <TabsContent value="guarantee">
          <Card>
            <CardHeader>
              <CardTitle>Configurações de Cards de Garantia e Confiança</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="guarantee-enabled">Exibir Cards de Garantia e Confiança</Label>
                <Switch
                  id="guarantee-enabled"
                  checked={settings.guaranteeCards.enabled}
                  onCheckedChange={(checked) => updateSetting('guaranteeCards', 'enabled', checked)}
                />
              </div>

              {settings.guaranteeCards.enabled && (
                <>
                  {/* Editor de Cards de Garantia */}
                  <GuaranteeCardsEditor
                    cards={settings.guaranteeCards.cards}
                    onCardsChange={(newCards) => updateSetting('guaranteeCards', 'cards', newCards)}
                    maxCards={6}
                  />

                  {/* Configurações de Exibição */}
                  <div className="pt-4 border-t">
                    <h4 className="font-medium mb-4">Configurações de Exibição</h4>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="guarantee-layout">Layout</Label>
                        <Select
                          value={settings.guaranteeCards.layout}
                          onValueChange={(value) => updateSetting('guaranteeCards', 'layout', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="horizontal">Horizontal</SelectItem>
                            <SelectItem value="vertical">Vertical</SelectItem>
                            <SelectItem value="grid">Grid</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="guarantee-bg">Cor de Fundo</Label>
                        <Select
                          value={settings.guaranteeCards.backgroundColor}
                          onValueChange={(value) => updateSetting('guaranteeCards', 'backgroundColor', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="bg-green-50">Verde Claro</SelectItem>
                            <SelectItem value="bg-blue-50">Azul Claro</SelectItem>
                            <SelectItem value="bg-gray-50">Cinza Claro</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Scarcity Settings */}
        <TabsContent value="scarcity">
          <Card>
            <CardHeader>
              <CardTitle>Configurações de Escassez</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="scarcity-enabled">Exibir Elementos de Escassez</Label>
                <Switch
                  id="scarcity-enabled"
                  checked={settings.scarcity.enabled}
                  onCheckedChange={(checked) => updateSetting('scarcity', 'enabled', checked)}
                />
              </div>

              {settings.scarcity.enabled && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="scarcity-total">Estoque Total</Label>
                      <Input
                        id="scarcity-total"
                        type="number"
                        value={settings.scarcity.totalStock}
                        onChange={(e) => updateSetting('scarcity', 'totalStock', parseInt(e.target.value))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="scarcity-sold">Vendidas</Label>
                      <Input
                        id="scarcity-sold"
                        type="number"
                        value={settings.scarcity.soldCount}
                        onChange={(e) => updateSetting('scarcity', 'soldCount', parseInt(e.target.value))}
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="scarcity-message">Mensagem</Label>
                    <Input
                      id="scarcity-message"
                      value={settings.scarcity.message}
                      onChange={(e) => updateSetting('scarcity', 'message', e.target.value)}
                      placeholder="Apenas {remaining} vagas restantes!"
                    />
                  </div>

                  <div>
                    <Label htmlFor="scarcity-variant">Variante</Label>
                    <Select
                      value={settings.scarcity.variant}
                      onValueChange={(value) => updateSetting('scarcity', 'variant', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="warning">Aviso</SelectItem>
                        <SelectItem value="danger">Perigo</SelectItem>
                        <SelectItem value="info">Informação</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Testimonials Settings */}
        <TabsContent value="testimonials">
          <Card>
            <CardHeader>
              <CardTitle>Configurações de Depoimentos</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="testimonials-enabled">Exibir Depoimentos de Clientes</Label>
                <Switch
                  id="testimonials-enabled"
                  checked={settings.testimonials.enabled}
                  onCheckedChange={(checked) => updateSetting('testimonials', 'enabled', checked)}
                />
              </div>

              {settings.testimonials.enabled && (
                <>
                  {/* Editor de Depoimentos */}
                  <TestimonialsEditor
                    testimonials={settings.testimonials.testimonials}
                    onTestimonialsChange={(newTestimonials) => updateSetting('testimonials', 'testimonials', newTestimonials)}
                    maxTestimonials={10}
                  />

                  {/* Configurações de Exibição */}
                  <div className="pt-4 border-t">
                    <h4 className="font-medium mb-4">Configurações de Exibição</h4>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="testimonials-max">Máximo de Depoimentos</Label>
                        <Input
                          id="testimonials-max"
                          type="number"
                          value={settings.testimonials.maxTestimonials}
                          onChange={(e) => updateSetting('testimonials', 'maxTestimonials', parseInt(e.target.value))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="testimonials-interval">Intervalo (ms)</Label>
                        <Input
                          id="testimonials-interval"
                          type="number"
                          value={settings.testimonials.autoPlayInterval}
                          onChange={(e) => updateSetting('testimonials', 'autoPlayInterval', parseInt(e.target.value))}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 mt-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="testimonials-autoplay">Reprodução Automática</Label>
                        <Switch
                          id="testimonials-autoplay"
                          checked={settings.testimonials.autoPlay}
                          onCheckedChange={(checked) => updateSetting('testimonials', 'autoPlay', checked)}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="testimonials-stars">Mostrar Estrelas</Label>
                        <Switch
                          id="testimonials-stars"
                          checked={settings.testimonials.showStars}
                          onCheckedChange={(checked) => updateSetting('testimonials', 'showStars', checked)}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="testimonials-avatars">Mostrar Avatares</Label>
                        <Switch
                          id="testimonials-avatars"
                          checked={settings.testimonials.showAvatars}
                          onCheckedChange={(checked) => updateSetting('testimonials', 'showAvatars', checked)}
                        />
                      </div>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Sidebar Settings */}
        <TabsContent value="sidebar">
          <Card>
            <CardHeader>
              <CardTitle>Configurações da Sidebar</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="sidebar-enabled">Exibir Sidebar</Label>
                <Switch
                  id="sidebar-enabled"
                  checked={settings.sidebar.enabled}
                  onCheckedChange={(checked) => updateSetting('sidebar', 'enabled', checked)}
                />
              </div>

              {settings.sidebar.enabled && (
                <>
                  <div>
                    <Label htmlFor="sidebar-banner">URL do Banner</Label>
                    <Input
                      id="sidebar-banner"
                      value={settings.sidebar.bannerUrl || ''}
                      onChange={(e) => updateSetting('sidebar', 'bannerUrl', e.target.value)}
                      placeholder="https://exemplo.com/sidebar-banner.jpg"
                    />
                  </div>

                  <div>
                    <Label htmlFor="sidebar-title">Título</Label>
                    <Input
                      id="sidebar-title"
                      value={settings.sidebar.title}
                      onChange={(e) => updateSetting('sidebar', 'title', e.target.value)}
                      placeholder="Informações Importantes"
                    />
                  </div>

                  <div>
                    <Label htmlFor="sidebar-content">Conteúdo</Label>
                    <Textarea
                      id="sidebar-content"
                      value={settings.sidebar.content}
                      onChange={(e) => updateSetting('sidebar', 'content', e.target.value)}
                      placeholder="Adicione informações úteis para seus clientes aqui."
                      rows={4}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="sidebar-bg">Cor de Fundo</Label>
                      <Select
                        value={settings.sidebar.backgroundColor}
                        onValueChange={(value) => updateSetting('sidebar', 'backgroundColor', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="bg-blue-50">Azul Claro</SelectItem>
                          <SelectItem value="bg-gray-50">Cinza Claro</SelectItem>
                          <SelectItem value="bg-green-50">Verde Claro</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="sidebar-text">Cor do Texto</Label>
                      <Select
                        value={settings.sidebar.textColor}
                        onValueChange={(value) => updateSetting('sidebar', 'textColor', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="text-blue-800">Azul Escuro</SelectItem>
                          <SelectItem value="text-gray-800">Cinza Escuro</SelectItem>
                          <SelectItem value="text-green-800">Verde Escuro</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
