"use client";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { UserMenu } from "@saas/shared/components/UserMenu";
import { Logo } from "@shared/components/Logo";
import { cn } from "@ui/lib";
import {
		ChevronRightIcon,
		HomeIcon,
		SettingsIcon,
		UserCog2Icon,
		UserCogIcon,
		DollarSignIcon,
		BuildingIcon,
		ShieldIcon,
		ShoppingCartIcon,
		PackageIcon,
		TrendingUpIcon,
		CreditCardIcon,
		UsersIcon,
		BarChart3Icon,
		WebhookIcon,
		ActivityIcon,
		FileTextIcon,
		ZapIcon,
		ChartSpline,
		UserIcon,
		Users2Icon,
		LandmarkIcon,
		PlayIcon,
		RefreshCwIcon,
		UserCheckIcon,
		MessageSquareIcon,
		BotIcon,
		MoreHorizontalIcon,
	} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";
import { OrganzationSelect } from "../../organizations/components/OrganizationSelect";
import { MembersBadge } from "../../organizations/components/MembersBadge";
import { DashIcon } from "@radix-ui/react-icons";
import { isAdmin, isSeller, isSuperAdmin } from "@repo/auth/lib/helper";

interface MenuItem {
	label: string;
	href: string;
	icon: React.ComponentType<{ className?: string }>;
	isActive: boolean;
	isSecondary?: boolean;
	roles?: string[]; // Roles que podem acessar este item
}

interface AppItem {
	id: string;
	name: string;
	description: string;
	icon: React.ComponentType<{ className?: string }>;
	status: 'active' | 'coming_soon' | 'inactive';
	href: string;
	badge?: string;
}

export function NavBar() {
	const t = useTranslations();
	const pathname = usePathname();
	const { user } = useSession();
	const { activeOrganization, isOrganizationAdmin } = useActiveOrganization();

	const { useSidebarLayout } = config.ui.saas;
	const isAdminRoute = pathname.startsWith("/app/backoffice") || pathname.startsWith("/app/admin");

	const basePath = activeOrganization
		? `/app/${activeOrganization.slug}`
		: "/app";

	// Função para verificar se o usuário tem permissão para ver um item do menu
	const canAccessMenuItem = (item: MenuItem): boolean => {
		if (!item.roles || item.roles.length === 0) return true;
		if (!user?.role) return false;

		// Special handling for organization admin role
		if (item.roles.includes("ORGANIZATION_ADMIN")) {
			return isOrganizationAdmin;
		}

		return item.roles.includes(user.role);
	};

	// Configuração dos apps BuckApps
	const buckApps: AppItem[] = [
		{
			id: 'member-area',
			name: 'Member Area',
			description: 'Área de membros e gestão de usuários',
			icon: UserCheckIcon,
			status: 'active',
			href: `${basePath}/member-area`,
		},
		{
			id: 'communities',
			name: 'Communities',
			description: 'Gestão de comunidades e grupos',
			icon: MessageSquareIcon,
			status: 'active',
			href: `${basePath}/communities`,
		},
		{
			id: 'ai-agents',
			name: 'AI Agents',
			description: 'Agentes de IA e automação',
			icon: BotIcon,
			status: 'coming_soon',
			href: `${basePath}/ai-agents`,
			badge: 'Em breve',
		},
	];

	// Menu completo para super admin (gestão completa do sistema)
	const adminMenuItems: MenuItem[] = [
		{
			label: "Dashboard",
			href: "/app/backoffice",
			icon: HomeIcon,
			isActive: pathname === "/app/backoffice",
		},
		{
			label: "Organizações",
			href: pathname.startsWith("/app/admin") ? "/app/admin/organizations" : "/app/backoffice/organizations",
			icon: BuildingIcon,
			isActive: pathname.startsWith("/app/backoffice/organizations") || pathname.startsWith("/app/admin/organizations"),
		},
		{
			label: "Usuários",
			href: pathname.startsWith("/app/admin") ? "/app/admin/users" : "/app/backoffice/users",
			icon: UsersIcon,
			isActive: pathname.startsWith("/app/backoffice/users") || pathname.startsWith("/app/admin/users"),
		},
		{
			label: "Transações",
			href: "/app/backoffice/transactions",
			icon: DollarSignIcon,
			isActive: pathname.startsWith("/app/backoffice/transactions"),
		},
		{
			label: "Pagamentos",
			href: "/app/backoffice/payments",
			icon: CreditCardIcon,
			isActive: pathname.startsWith("/app/backoffice/payments"),
		},
		{
			label: "Saques",
			href: "/app/backoffice/withdrawals",
			icon: TrendingUpIcon,
			isActive: pathname.startsWith("/app/backoffice/withdrawals"),
		},
		{
			label: "Estornos",
			href: "/app/backoffice/refunds",
			icon: ActivityIcon,
			isActive: pathname.startsWith("/app/backoffice/refunds"),
		},
		{
			label: "Gateways",
			href: "/app/backoffice/gateways",
			icon: WebhookIcon,
			isActive: pathname.startsWith("/app/backoffice/gateways"),
		},
		{
			label: "Analytics",
			href: "/app/backoffice/analytics",
			icon: BarChart3Icon,
			isActive: pathname.startsWith("/app/backoffice/analytics"),
		},
		{
			label: "Logs",
			href: "/app/backoffice/logs",
			icon: FileTextIcon,
			isActive: pathname.startsWith("/app/backoffice/logs"),
		},
		{
			label: "Configurações",
			href: "/app/backoffice/settings",
			icon: SettingsIcon,
			isActive: pathname.startsWith("/app/backoffice/settings"),
		},
	];

	// Menu simplificado para rota /app (seleção de empresas)
	const appMenuItems: MenuItem[] = [
		{
			label: "Minha Conta",
			href: "/app/account",
			icon: UserCog2Icon,
			isActive: pathname.includes("/account"),
			roles: ["SELLER", "ADMIN", "SUPER_ADMIN"],
		},
		{
			label: "Planos",
			href: "/app/choose-plan",
			icon: CreditCardIcon,
			isActive: pathname.includes("/choose-plan"),
			roles: ["SELLER", "ADMIN", "SUPER_ADMIN"],
		},
		{
			label: "Onboarding",
			href: "/app/onboarding",
			icon: ZapIcon,
			isActive: pathname.includes("/onboarding"),
			roles: ["SELLER", "ADMIN", "SUPER_ADMIN"],
		},
		{
			label: "Back Office",
			href: "/app/backoffice",
			icon: ShieldIcon,
			isActive: pathname.startsWith("/app/backoffice"),
			roles: ["ADMIN", "SUPER_ADMIN"],
		},
	];

	// Menu completo para rotas de tenant (dentro de uma empresa)
	const tenantMenuItems: MenuItem[] = [
		{
			label: "Dashboard",
			href: basePath,
			icon: ChartSpline,
			isActive: pathname === basePath,
			roles: ["SELLER", "ADMIN", "SUPER_ADMIN"],
		},
		{
			label: "Vendas",
			href: `${basePath}/sales`,
			icon: ShoppingCartIcon,
			isActive: pathname.includes("/sales"),
			roles: ["SELLER", "ADMIN", "SUPER_ADMIN"],
		},
		{
			label: "Produtos",
			href: `${basePath}/products`,
			icon: PackageIcon,
			isActive: pathname.includes("/products"),
			roles: ["SELLER", "ADMIN", "SUPER_ADMIN"],
		},
		{
			label: "Financeiro",
			href: `${basePath}/financial`,
			icon: LandmarkIcon,
			isActive: pathname.includes("/financial"),
			roles: ["ADMIN", "SUPER_ADMIN"], // Apenas admins podem ver finanças
		},
		{
			label: "Clientes",
			href: `${basePath}/customers`,
			icon: UserIcon,
			isActive: pathname.includes("/customers"),
			roles: ["SELLER", "ADMIN", "SUPER_ADMIN"],
		},
		{
			label: "Analytics",
			href: `${basePath}/analytics`,
			icon: BarChart3Icon,
			isActive: pathname.includes("/analytics"),
			roles: ["SELLER", "ADMIN", "SUPER_ADMIN"],
		},
		{
			label: "Integrações",
			href: `${basePath}/integrations`,
			icon: ZapIcon,
			isActive: pathname.includes("/integrations"),
			roles: ["ADMIN", "SUPER_ADMIN"], // Apenas admins podem configurar integrações
		},
		{
			label: "Colaboradores",
			href: `${basePath}/members`,
			icon: Users2Icon,
			isActive: pathname.includes("/members"),
			roles: ["ORGANIZATION_ADMIN"], // Special role for organization admins
		},
		// Menu de Conta - centraliza todas as configurações
		{
			label: "Minha Conta",
			href: `${basePath}/account`,
			icon: UserCog2Icon,
			isActive: pathname.includes("/account") || pathname.includes("/billing") || pathname.includes("/settings"),
			roles: ["SELLER", "ADMIN", "SUPER_ADMIN"],
		},
	];

	// Menu ativo baseado na rota e filtrado por role
	const isAppRoot = pathname === "/app";
	const baseMenuItems = isAdminRoute ? adminMenuItems : (isAppRoot ? appMenuItems : tenantMenuItems);
	const activeMenuItems = baseMenuItems.filter(canAccessMenuItem);

	return (
		<nav
			className={cn("w-full", {
				"w-full md:fixed md:top-0 md:left-0 md:h-full md:w-[280px]":
					useSidebarLayout,
			})}
		>
			<div
				className={cn("container max-w-6xl py-4", {
					"container max-w-6xl py-4 md:flex md:h-full md:flex-col md:px-4 md:pt-6 md:pb-0":
						useSidebarLayout,
				})}
			>
				<div className="flex flex-wrap items-center justify-between gap-4">
					<div
						className={cn("flex items-center gap-4 md:gap-2", {
							"md:flex md:w-full md:flex-col md:items-stretch md:align-stretch":
								useSidebarLayout,
						})}
					>
						<Link href="/app" className="block">
							<Logo />
						</Link>

						{/* Indicador de modo admin */}
						{isAdminRoute && (
							<div className="flex items-center gap-2 px-2 py-1 rounded-full bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200 text-xs font-medium">
								<ShieldIcon className="h-3 w-3" />
								Admin
							</div>
						)}

						{/* Seletor de organização apenas para rotas de tenant */}
						{!isAdminRoute &&
							config.organizations.enable &&
							!config.organizations.hideOrganization && (
								<>
									<span
										className={cn(
											"hidden opacity-30 md:block",
											{
												"md:hidden": useSidebarLayout,
											},
										)}
									>
										<ChevronRightIcon className="size-4" />
									</span>

									<div className={cn("flex items-center gap-4", {
										"md:flex-col md:items-stretch md:gap-2": useSidebarLayout,
									})}>
										<OrganzationSelect
											className={cn({
												"md:-mx-1 md:mt-2":
													useSidebarLayout,
											})}
										/>


									</div>
								</>
							)}
					</div>

					<div
						className={cn(
							"mr-0 ml-auto flex items-center justify-end gap-4",
							{
								"md:hidden": useSidebarLayout,
							},
						)}
					>
						<UserMenu />
					</div>
				</div>

				<div
					className={cn(
						"no-scrollbar -mx-4 -mb-4 mt-6 flex list-none items-center justify-start gap-4 overflow-x-auto px-4 text-sm",
						{
							"md:mx-0 md:my-4 md:flex md:flex-col md:items-stretch md:gap-0 md:px-0 md:overflow-y-auto md:flex-1":
								useSidebarLayout,
						},
					)}
				>
					{useSidebarLayout ? (
						// Layout de sidebar simplificado
						<div className="space-y-2">
							{/* Menu principal */}
							<div className="space-y-1">
								{activeMenuItems
									.filter(item => !item.isSecondary)
									.map((menuItem) => (
										<Link
											key={menuItem.href}
											href={menuItem.href}
											className={cn(
												"flex items-center gap-3 px-4 py-3 text-sm font-medium rounded-lg mx-1 transition-all duration-200 group",
												menuItem.isActive
													? "bg-primary text-primary-foreground shadow-sm"
													: "text-muted-foreground hover:text-foreground hover:bg-muted/50"
											)}
											prefetch
										>
											<menuItem.icon className={cn(
												"size-5 shrink-0 transition-transform duration-200",
												menuItem.isActive ? "" : "group-hover:scale-110"
											)} />
											<span className="font-medium">{menuItem.label}</span>
										</Link>
									))}
							</div>

							{/* Separador visual */}
							{activeMenuItems.some(item => item.isSecondary) && (
								<div className="px-4 py-2">
									<div className="h-px bg-border" />
								</div>
							)}

							{/* Menu secundário */}
							{activeMenuItems.some(item => item.isSecondary) && (
								<div className="space-y-1">
									{activeMenuItems
										.filter(item => item.isSecondary)
										.map((menuItem) => (
											<Link
												key={menuItem.href}
												href={menuItem.href}
												className={cn(
													"flex items-center gap-3 px-4 py-3 text-sm font-medium rounded-lg mx-1 transition-all duration-200 group text-muted-foreground hover:text-foreground hover:bg-muted/50",
													menuItem.isActive && "bg-muted text-foreground"
												)}
												prefetch
											>
												<menuItem.icon className="size-5 shrink-0 transition-transform duration-200 group-hover:scale-110" />
												<span className="font-medium">{menuItem.label}</span>
											</Link>
										))}
								</div>
							)}

							{/* Separador visual para Apps */}
							<div className="px-4 py-2">
								<div className="h-px bg-border" />
							</div>

							{/* Seção Apps */}
							<div className="space-y-2">
								<div className="px-4 py-1">
									<h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
										Apps
									</h3>
								</div>

								<div className="space-y-1">
									{buckApps.map((app) => (
										<div key={app.id} className="mx-1">
											{app.status === 'coming_soon' ? (
												<div className="flex items-center gap-3 px-4 py-3 text-sm font-medium rounded-lg text-muted-foreground cursor-not-allowed opacity-60">
													<app.icon className="size-5 shrink-0" />
													<div className="flex-1 flex items-center justify-between">
														<span className="font-medium">{app.name}</span>
														{app.badge && (
															<span className="text-xs px-2 py-1 bg-muted text-muted-foreground rounded-full">
																{app.badge}
															</span>
														)}
													</div>
												</div>
											) : (
												<Link
													href={app.href}
													className={cn(
														"flex items-center gap-3 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 group",
														pathname.includes(app.href)
															? "bg-primary text-primary-foreground shadow-sm"
															: "text-muted-foreground hover:text-foreground hover:bg-muted/50"
													)}
													prefetch
												>
													<app.icon className={cn(
														"size-5 shrink-0 transition-transform duration-200",
														pathname.includes(app.href) ? "" : "group-hover:scale-110"
													)} />
													<div className="flex-1 flex items-center justify-between">
														<span className="font-medium">{app.name}</span>
														{app.status === 'active' && (
															<div className="w-2 h-2 bg-green-500 rounded-full" />
														)}
													</div>
												</Link>
											)}
										</div>
									))}
								</div>
							</div>
						</div>
					) : (
						// Layout horizontal simplificado
						<>
							{activeMenuItems
								.filter(item => !item.isSecondary)
								.map((menuItem) => (
									<div key={menuItem.href} className="flex-shrink-0">
										<Link
											href={menuItem.href}
											className={cn(
												"flex items-center gap-2 whitespace-nowrap border-b-2 px-1 pb-3",
												menuItem.isActive
													? "border-primary font-bold"
													: "border-transparent"
											)}
											prefetch
										>
											<menuItem.icon
												className={`size-5 shrink-0 ${
													menuItem.isActive
														? "text-primary"
														: "opacity-50"
												}`}
											/>
											<span>{menuItem.label}</span>
										</Link>
									</div>
								))}

							{/* Separador para Apps no layout horizontal */}
							<div className="flex-shrink-0 px-2">
								<div className="w-px h-6 bg-border" />
							</div>

							{/* Apps no layout horizontal */}
							{buckApps.map((app) => (
								<div key={app.href} className="flex-shrink-0">
									{app.status === 'coming_soon' ? (
										<div className="flex items-center gap-2 whitespace-nowrap border-b-2 px-1 pb-3 border-transparent opacity-50 cursor-not-allowed">
											<app.icon className="size-5 shrink-0 opacity-50" />
											<span className="text-sm">{app.name}</span>
											{app.badge && (
												<span className="text-xs px-1 py-0.5 bg-muted text-muted-foreground rounded">
													{app.badge}
												</span>
											)}
										</div>
									) : (
										<Link
											href={app.href}
											className={cn(
												"flex items-center gap-2 whitespace-nowrap border-b-2 px-1 pb-3",
												pathname.includes(app.href)
													? "border-primary font-bold"
													: "border-transparent"
											)}
											prefetch
										>
											<app.icon
												className={`size-5 shrink-0 ${
													pathname.includes(app.href)
														? "text-primary"
														: "opacity-50"
												}`}
											/>
											<span className="text-sm">{app.name}</span>
											{app.status === 'active' && (
												<div className="w-2 h-2 bg-green-500 rounded-full" />
											)}
										</Link>
									)}
								</div>
							))}
						</>
					)}

					{/* Botão para alternar entre admin e tenant - apenas na rota do backoffice */}
					{isAdmin(user) && isAdminRoute && (
						<div className={cn("flex-shrink-0", {
							"mt-auto mb-4": useSidebarLayout
						})}>
							<Link
								href="/app"
								className={cn(
									"flex items-center gap-2 whitespace-nowrap border-b-2 px-1 pb-1 border-transparent hover:border-primary/50 transition-colors",
									{
										"border-b-0 px-4 py-2 mx-1 rounded-lg hover:bg-muted text-muted-foreground hover:text-foreground":
											useSidebarLayout,
									},
								)}
							>
								<BuildingIcon className="size-5 shrink-0" />
								<span>Voltar ao App</span>
							</Link>
						</div>
					)}
				</div>

				<div
					className={cn(
						"-mx-4 md:-mx-4 mt-auto mb-0 hidden p-4 md:p-4",
						{
							"md:block": useSidebarLayout,
						},
					)}
				>
					<UserMenu showUserName />
				</div>
			</div>
		</nav>
	);
}
