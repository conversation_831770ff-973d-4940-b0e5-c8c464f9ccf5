export const STORAGE_CONFIG = {
  buckets: {
    onboardingDocuments: "onboarding-documents",
    userDocuments: "user-documents",
    companyDocuments: "company-documents",
    checkoutBanners: "supgateway", // Usar o bucket existente
    testimonialAvatars: "supgateway", // Usar o mesmo bucket para avatares de depoimentos
  },
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: [
    "image/jpeg",
    "image/png",
    "image/jpg",
    "image/webp",
    "application/pdf",
  ],
  uploadTimeout: 30000, // 30 seconds
} as const;

export type StorageBucket = keyof typeof STORAGE_CONFIG.buckets;
